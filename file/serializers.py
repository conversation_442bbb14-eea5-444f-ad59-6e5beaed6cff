from rest_framework import serializers
from core.parse_time import ShanghaiFriendlyDateTimeField
from file.models import OpportunityAttachment


# 商机附件序列化器
class OpportunityAttachmentSerializer(serializers.ModelSerializer):
    # 附件URL（自动生成签名链接）
    attachment_url = serializers.SerializerMethodField()
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()

    class Meta:
        model = OpportunityAttachment
        fields = ['id', 'attachment', 'attachment_url', 'created_at', 'updated_at']
        read_only_fields = ['id', 'attachment_url', 'created_at', 'updated_at']

    def get_attachment_url(self, obj):
        """
        获取文件的签名URL
        由于配置了 querystring_auth=True，Django会自动生成签名链接
        """
        if obj.attachment:
            return obj.attachment.url
        return None


# 商机附件创建序列化器
class OpportunityAttachmentCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = OpportunityAttachment
        fields = ['attachment']

    def validate_attachment(self, value):
        """
        验证上传的文件
        """
        # 检查文件大小（例如限制为10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        if value.size > max_size:
            raise serializers.ValidationError("文件大小不能超过10MB")
        
        # 可以添加更多验证，如文件类型等
        return value
